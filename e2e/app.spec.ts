import { test, expect, waitForAppReady, getMainWindow, getWindowByTitle } from './electron-utils';

test.describe('SpeakMCP App', () => {
  test('should launch and show main window', async ({ electronApp, page }) => {
    // Wait for the app to be ready
    await waitForAppReady(page);

    // Check if the window is visible
    expect(await page.isVisible('body')).toBeTruthy();

    // Check the window title
    const title = await page.title();
    expect(title).toContain('SpeakMCP');
  });

  test('should have correct window properties', async ({ electronApp }) => {
    const mainWindow = await getMainWindow(electronApp);
    
    // Check if window is visible
    expect(await mainWindow.isVisible('body')).toBeTruthy();
    
    // You can add more window property checks here
    const boundingBox = await mainWindow.viewportSize();
    expect(boundingBox).toBeTruthy();
  });

  test('should handle window creation', async ({ electronApp }) => {
    // Test that the app creates the expected windows
    const windows = electronApp.windows();
    expect(windows.length).toBeGreaterThan(0);

    // Check if main window exists
    const mainWindow = await getMainWindow(electronApp);
    expect(mainWindow).toBeTruthy();
  });

  test('should respond to keyboard events', async ({ page }) => {
    await waitForAppReady(page);

    // Test keyboard interaction - this will depend on your app's specific behavior
    // For example, if your app has a search input:
    // await page.fill('[data-testid="search-input"]', 'test query');
    // await page.keyboard.press('Enter');
    
    // Or test global shortcuts if your app supports them
    // await page.keyboard.press('CommandOrControl+K');
    
    // For now, just test that the page can receive focus
    await page.focus('body');
    expect(await page.isVisible('body')).toBeTruthy();
  });

  test('should handle menu interactions', async ({ electronApp, page }) => {
    await waitForAppReady(page);

    // Test menu interactions - this depends on your app's menu structure
    // You might need to use electronApp.evaluate() to interact with the main process
    
    // Example of evaluating code in the main process:
    const appVersion = await electronApp.evaluate(async ({ app }) => {
      return app.getVersion();
    });
    
    expect(appVersion).toBeTruthy();
  });

  test('should handle tray interactions', async ({ electronApp }) => {
    // Test tray functionality if your app has a system tray
    // This might require specific setup depending on your tray implementation
    
    const windows = electronApp.windows();
    expect(windows.length).toBeGreaterThan(0);
  });
});
