import { test as base, expect, _electron as electron } from '@playwright/test';
import { ElectronApplication, Page } from 'playwright';
import { resolve } from 'path';

export { expect };

type ElectronFixtures = {
  electronApp: ElectronApplication;
  page: Page;
};

export const test = base.extend<ElectronFixtures>({
  electronApp: async ({}, use) => {
    // Launch Electron app
    const electronApp = await electron.launch({
      args: [resolve(__dirname, '../out/main/index.js')],
      // Enable debugging if needed
      // executablePath: resolve(__dirname, '../node_modules/.bin/electron'),
    });

    // Wait for the first window to be created
    const firstWindow = await electronApp.firstWindow();
    await firstWindow.waitForLoadState('domcontentloaded');

    await use(electronApp);

    // Clean up
    await electronApp.close();
  },

  page: async ({ electronApp }, use) => {
    // Get the first window (main window)
    const page = await electronApp.firstWindow();
    await use(page);
  },
});

/**
 * Helper function to wait for the app to be ready
 */
export async function waitForAppReady(page: Page) {
  // Wait for the app to be fully loaded
  await page.waitForLoadState('networkidle');
  
  // You can add more specific waits here based on your app's loading behavior
  // For example, wait for a specific element to be visible
  // await page.waitForSelector('[data-testid="app-ready"]');
}

/**
 * Helper function to get the main window
 */
export async function getMainWindow(electronApp: ElectronApplication): Promise<Page> {
  const windows = electronApp.windows();
  return windows[0];
}

/**
 * Helper function to get a window by title
 */
export async function getWindowByTitle(electronApp: ElectronApplication, title: string): Promise<Page | null> {
  const windows = electronApp.windows();
  for (const window of windows) {
    const windowTitle = await window.title();
    if (windowTitle.includes(title)) {
      return window;
    }
  }
  return null;
}

/**
 * Helper function to simulate keyboard shortcuts
 */
export async function pressShortcut(page: Page, shortcut: string) {
  await page.keyboard.press(shortcut);
}

/**
 * Helper function to check if accessibility is granted (for macOS)
 */
export async function checkAccessibilityPermissions(electronApp: ElectronApplication): Promise<boolean> {
  // This would need to be implemented based on your app's accessibility check
  // You might need to expose this through IPC from your main process
  return true; // Placeholder
}
