# End-to-End Testing with <PERSON><PERSON>

This directory contains end-to-end tests for the SpeakMCP Electron application using <PERSON><PERSON>'s Electron API.

## Setup

The e2e testing setup is already configured. To run the tests:

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Install Playwright browsers**:
   ```bash
   npx playwright install
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

4. **Run the tests**:
   ```bash
   npm run test:e2e
   ```

## Available Test Scripts

- `npm run test:e2e` - Run all e2e tests headlessly
- `npm run test:e2e:ui` - Run tests with Playwright UI mode
- `npm run test:e2e:debug` - Run tests in debug mode
- `npm run test:e2e:headed` - Run tests in headed mode (visible browser)

## Test Structure

### Core Test Files

- **`app.spec.ts`** - Basic application functionality tests
  - Window creation and properties
  - Basic UI interactions
  - Menu and tray functionality

- **`mcp-integration.spec.ts`** - MCP (Model Context Protocol) integration tests
  - MCP service initialization
  - Tool execution
  - Server lifecycle management
  - Error handling

- **`conversation.spec.ts`** - Conversation management tests
  - Conversation creation and persistence
  - Message handling and formatting
  - Search and filtering
  - Export/import functionality

- **`keyboard-accessibility.spec.ts`** - Keyboard and accessibility tests
  - Global keyboard shortcuts
  - Accessibility permissions
  - UI keyboard navigation
  - Speech recognition features

### Utilities

- **`electron-utils.ts`** - Helper utilities for Electron testing
  - Custom test fixtures for Electron apps
  - Window management helpers
  - Keyboard shortcut helpers
  - App readiness utilities

## Writing Tests

### Basic Test Structure

```typescript
import { test, expect, waitForAppReady } from './electron-utils';

test.describe('Feature Name', () => {
  test('should do something', async ({ electronApp, page }) => {
    await waitForAppReady(page);
    
    // Your test code here
    expect(something).toBeTruthy();
  });
});
```

### Accessing Main Process

To interact with the main process (e.g., testing MCP service):

```typescript
const result = await electronApp.evaluate(async ({ app }) => {
  // Code running in main process
  return someMainProcessFunction();
});
```

### Testing UI Interactions

```typescript
// Click elements
await page.click('[data-testid="button"]');

// Fill forms
await page.fill('[data-testid="input"]', 'value');

// Wait for elements
await page.waitForSelector('[data-testid="result"]');

// Check visibility
expect(await page.isVisible('[data-testid="element"]')).toBeTruthy();
```

### Testing Keyboard Shortcuts

```typescript
import { pressShortcut } from './electron-utils';

await pressShortcut(page, 'CommandOrControl+K');
```

## Configuration

The Playwright configuration is in `playwright.config.ts` at the project root. Key settings:

- **Test directory**: `./e2e`
- **Parallel execution**: Enabled for faster test runs
- **Retries**: 2 retries on CI, 0 locally
- **Reporter**: HTML reporter for detailed results

## CI/CD Integration

Tests run automatically on:
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop` branches

The CI workflow runs tests on:
- Ubuntu (Linux)
- Windows
- macOS

## Debugging Tests

### Local Debugging

1. **Use debug mode**:
   ```bash
   npm run test:e2e:debug
   ```

2. **Use headed mode** to see the browser:
   ```bash
   npm run test:e2e:headed
   ```

3. **Use UI mode** for interactive debugging:
   ```bash
   npm run test:e2e:ui
   ```

### Adding Debug Information

```typescript
// Add console logs
console.log('Debug info:', await page.textContent('[data-testid="element"]'));

// Take screenshots
await page.screenshot({ path: 'debug-screenshot.png' });

// Pause execution
await page.pause();
```

## Best Practices

1. **Use data-testid attributes** for reliable element selection
2. **Wait for app readiness** before interacting with elements
3. **Test real user workflows** rather than implementation details
4. **Keep tests independent** - each test should work in isolation
5. **Use descriptive test names** that explain what is being tested
6. **Group related tests** using `test.describe()`

## Troubleshooting

### Common Issues

1. **App not building**: Ensure `npm run build` completes successfully
2. **Tests timing out**: Increase timeout or add proper waits
3. **Elements not found**: Check selectors and ensure elements exist
4. **Permission issues**: Some features may require specific permissions

### Platform-Specific Issues

- **macOS**: Accessibility permissions may be required
- **Windows**: Some shortcuts may behave differently
- **Linux**: Display server setup may be needed in CI

## Future Enhancements

Consider adding tests for:
- File system operations
- Network requests and responses
- Audio/speech recognition features
- System integration features
- Performance and memory usage
- Auto-updater functionality
