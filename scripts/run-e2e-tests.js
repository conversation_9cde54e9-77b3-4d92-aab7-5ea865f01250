#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON>t to run e2e tests with proper setup
 * Ensures the app is built before running Playwright tests
 */

const PROJECT_ROOT = path.join(__dirname, '..');
const OUT_DIR = path.join(PROJECT_ROOT, 'out');

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      cwd: PROJECT_ROOT,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

function checkBuildExists() {
  const mainIndexPath = path.join(OUT_DIR, 'main', 'index.js');
  return fs.existsSync(mainIndexPath);
}

async function main() {
  try {
    console.log('🚀 Starting e2e test setup...');

    // Check if build exists
    if (!checkBuildExists()) {
      console.log('📦 Build not found, building application...');
      await runCommand('npm', ['run', 'build']);
      console.log('✅ Build completed');
    } else {
      console.log('✅ Build already exists');
    }

    // Install Playwright browsers if needed
    console.log('🎭 Ensuring Playwright browsers are installed...');
    await runCommand('npx', ['playwright', 'install', '--with-deps']);
    console.log('✅ Playwright browsers ready');

    // Run the tests
    console.log('🧪 Running e2e tests...');
    const testArgs = process.argv.slice(2);
    await runCommand('npx', ['playwright', 'test', ...testArgs]);
    console.log('✅ Tests completed successfully');

  } catch (error) {
    console.error('❌ Error running e2e tests:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
